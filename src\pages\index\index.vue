<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationBarTitleText: '特喜',
  },
}
</route>
<template>
  <view class="wrapper">
    <!-- <KeFu /> -->
    <view class="px-4">
      <view class="banner">
        <wd-swiper
          :list="swiperList"
          autoplay
          v-model:current="current"
          @click="handleClick"
          :height="146"
          imageMode="widthFix"
          @change="onChange"
        ></wd-swiper>
      </view>
    </view>
    <!-- 热门活动板块 -->
    <view class="hot-section">
      <view class="section-header">
        <view class="section-title">热门活动</view>
        <view class="more-btn" @click="toActivityList">
          更多活动
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
      <view class="activity-list">
        <scroll-view class="scroll-view_H" scroll-x="true" scroll-left="120">
          <view
            v-for="item in hotActivities"
            :key="item.id"
            class="activity-item"
            @click="toActivityDetail(item.id)"
          >
            <view class="activity-image-wrapper">
              <image
                class="activity-image"
                :src="item.image_input?.[0] || '/static/images/banner.png'"
                mode="aspectFill"
              ></image>
              <view class="activity-status">报名中</view>
            </view>
            <view class="activity-info">
              <view class="activity-title">{{ item.title }}</view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 热门话题板块 -->
    <view class="hot-section mt-2">
      <view class="section-header">
        <view class="section-title">热门话题</view>
        <view class="more-btn" @click="toTopicList">
          更多话题
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
      <view class="topic-list">
        <view
          v-for="item in hotTopics"
          :key="item.id"
          class="topic-item"
          @click="toTopicDetail(item.id)"
        >
          <view class="topic-image-wrapper">
            <image
              class="topic-image"
              :src="item.image_input?.[0] || '/static/images/banner.png'"
              mode="aspectFill"
            ></image>
          </view>
          <view class="topic-info">
            <view class="topic-title">{{ item.title }}</view>
            <view class="topic-author">
              <image
                class="author-avatar"
                :src="item.user?.avatar || '/static/images/login-female-default.png'"
                mode="aspectFill"
              ></image>
              <text class="author-name">{{ item.user?.nickname || '美丽人生' }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 发帖按钮 -->
    <button hover-class="button-hover" class="post-btn" @click="toPost">
      <wd-icon name="add" size="12"></wd-icon>
      发帖
    </button>
  </view>
</template>

<script lang="ts" setup>
import { indexUser } from '@/service/user'
import { getBanners, getHotActivities, getHotTopics } from '@/service/public/index'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'
// import KeFu from '@/components/KeFu/index.vue'

//
defineOptions({
  name: 'Home',
})
const duration = ref(500)
const current = ref(0)

const { onShareAppMessage, onShareTimeline } = useShare()
const userStore = useUserStore()
const { isComplete } = storeToRefs(userStore)
const pageData = reactive({
  cityId: '',
  nickname: '',
  page: 1,
  limit: 10,
  loading: false,
  loadend: false,
})
const self = reactive<{
  userList: any[]
  current: number
}>({
  userList: [],
  current: 0,
})

const handleCityChange = (e) => {
  pageData.cityId = e
  reset()
}
const handleSearch = (e) => {
  pageData.nickname = e
  reset()
}
const reset = () => {
  pageData.page = 1
  pageData.loadend = false
  self.userList = []
  loadUserList()
}

// 加载首页推荐人员
const loadUserList = async () => {
  if (pageData.loading || pageData.loadend) return
  pageData.loading = true

  const data = {
    city_id: pageData.cityId,
    nickname: pageData.nickname,
    page: pageData.page,
    limit: pageData.limit,
  }
  const [res, err] = await indexUser(data)
  if (res) {
    res.data.forEach((element) => {
      if (element.life_img) {
        element.coverImg = element.life_img.split(',')[0]
      } else {
        element.coverImg = '/static/images/home-indo.png'
      }
      if (element.cards) {
        element.cardsImg = element.cards.split(',')
      } else {
        element.cardsImg = []
      }
    })
    const list = res.data
    const loadEnd = list.length < pageData.limit
    pageData.loadend = loadEnd
    pageData.page++
    self.userList = [...self.userList, ...list]
  }
  pageData.loading = false
}
// 跳转到用户详情
const toUserDetail = (item) => {
  uni.navigateTo({
    url: '/pages/users/profile-detail?uid=' + item.uid, // 跳转的页面
  })
}

const swiperList = ref(['/static/images/banner.png'])
const hotActivities = ref([])
const hotTopics = ref([])

function handleClick(e) {
  console.log(e)
}
function onChange(e) {
  console.log(e)
}

// 加载banner
const loadBanner = async () => {
  const [res, err] = await getBanners()
  if (res) {
    swiperList.value = res.data.map((item) => item.img)
  }
}

// 加载热门活动
const loadHotActivities = async () => {
  const [res, err] = await getHotActivities({ limit: 2 })
  if (res) {
    hotActivities.value = res.data || []
  } else {
    // 使用测试数据
    hotActivities.value = [
      {
        id: 1,
        title: '川西大环线7天6晚',
        address: '稻城亚丁+四姑娘山+色达',
        add_time_date: '8月26日 周一',
        image_input: ['/static/images/banner.png'],
        status: 1,
      },
      {
        id: 2,
        title: '特斯拉后备箱集市',
        address: '成都市天府新区',
        add_time_date: '8月28日 周三',
        image_input: ['/static/images/bg1.png'],
        status: 1,
      },
    ]
  }
}

// 加载热门话题
const loadHotTopics = async () => {
  const [res, err] = await getHotTopics({ limit: 2 })
  if (res) {
    hotTopics.value = res.data || []
  } else {
    // 使用测试数据
    hotTopics.value = [
      {
        id: 1,
        title: '请问特斯拉4S店贴的3M膜怎么样？',
        image_input: ['/static/images/banner.png'],
        user: {
          nickname: '美丽人生',
          avatar: '/static/images/login-female-default.png',
        },
        add_time_date: '2小时前',
      },
      {
        id: 2,
        title: '贴车衣有必要吗？',
        image_input: ['/static/images/bg1.png'],
        user: {
          nickname: '美丽人生',
          avatar: '/static/images/login-male-default.png',
        },
        add_time_date: '5小时前',
      },
    ]
  }
}

// 跳转方法
const toActivityList = () => {
  uni.navigateTo({
    url: '/pages/activity/activity-list',
  })
}

const toActivityDetail = (id) => {
  uni.navigateTo({
    url: '/pages/shequ/detail?id=' + id,
  })
}

const toTopicList = () => {
  uni.navigateTo({
    url: '/pages/shequ/index',
  })
}

const toTopicDetail = (id) => {
  uni.navigateTo({
    url: '/pages/shequ/detail?id=' + id,
  })
}

const toPost = () => {
  uni.navigateTo({
    url: '/pages/shequ/index',
  })
}

loadUserList()
loadBanner()
loadHotActivities()
loadHotTopics()

const onSwiperChange = (e) => {
  self.current = e.detail.current
  if (e.detail.current === self.userList.length - 1) {
    loadUserList()
  }
}

// 监听滚动高度
onPageScroll((res) => {
  uni.$emit('onPageScroll', res.scrollTop)
})
</script>

<style lang="scss" scoped>
.wrapper {
  position: relative;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.cover-wrap {
  width: 100%;
  margin-top: 50rpx;
}

.swiper {
  height: 930rpx;
}

.swiper-item {
  padding: 0 10rpx;
  border-radius: 0 0 20rpx 20rpx;
  transition: all 0.2s ease-in-out;
  transform: scale(0.96);

  .cover-img {
    position: relative;

    .image {
      width: 100%;
      height: 670rpx;
      border-radius: 20rpx 20rpx 0 0;
    }

    .heart {
      position: absolute;
      right: 30rpx;
      bottom: 50rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 58rpx;
      height: 58rpx;
      background: linear-gradient(-31deg, #ff94b2, #f9225e);
      border-radius: 50%;
      box-shadow: 0rpx 3rpx 12rpx 1rpx rgba(0, 0, 0, 0.31);

      .icon-heart {
        width: 30rpx;
        height: 27rpx;
      }
    }
  }

  .cover-info {
    position: relative;
    padding: 32rpx 28rpx 28rpx;
    background-color: #fff;
    border-radius: 0 0 20rpx 20rpx;
    box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);

    .avatar {
      position: absolute;
      top: -96rpx;
      left: 24rpx;
      width: 106rpx;
      height: 106rpx;
      overflow: hidden;
      border: 3px solid #ffffff;
      border-radius: 8rpx;
      box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.23);

      .image {
        width: 100%;
        height: 100%;
      }
    }

    .nickname-wrap {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .nickname {
        font-size: 34rpx;
        font-weight: bold;
        color: #303030;
      }

      .real-name-status {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8rpx 12rpx;
        margin-left: 20rpx;
        font-size: 20rpx;
        color: #ffffff;
        border-radius: 36rpx;

        .icon {
          width: 22rpx;
          height: 22rpx;
          margin-right: 6rpx;
        }
      }

      .yes {
        background: linear-gradient(-31deg, #ff94b2, #f9225e);
      }

      .level-img {
        display: flex;
        column-gap: 8rpx;
        align-items: center;
        margin-left: 10rpx;

        .image {
          width: 140rpx;
          height: 36rpx;
        }
      }

      .desc {
        font-size: 20rpx;
        font-weight: 500;
        color: #ff225e;
      }
    }

    .tags {
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      margin-top: 36rpx;

      .tag {
        &__item {
          display: flex;
          align-items: center;
          margin-right: 30rpx;
          margin-bottom: 15rpx;
          font-size: 24rpx;
          font-weight: 400;
          color: #303030;
        }
      }

      .tag__icon__box {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 38rpx;
        height: 38rpx;
        margin-right: 20rpx;
        background-color: #fdf3f4;
        border-radius: 50%;
      }

      .tag__icon__box-nan {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 38rpx;
        height: 38rpx;
        margin-right: 20rpx;
        border-radius: 50%;
      }
    }
  }
}

.swiper-item-active {
  transform: scale(1);
}

// 热门板块样式
.hot-section {
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 30rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #303030;
    }

    .more-btn {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      color: #999;

      .iconfont {
        margin-left: 8rpx;
        font-size: 20rpx;
      }
    }
  }
}

// 活动列表样式
.activity-list {
  padding: 40rpx 20rpx 20rpx;
  white-space: nowrap;
  background-color: white;
  width: 100%;
  .activity-item {
    display: inline-block;
    width: 460rpx;
    .activity-image-wrapper {
      position: relative;
      margin-right: 20rpx;
      .activity-image {
        width: 100%;
        height: 160rpx;
        border-radius: 16rpx;
        margin-bottom: 20rpx;
      }
      .activity-status {
        position: absolute;
        top: 0;
        left: 0;
        padding: 4rpx 12rpx;
        font-size: 24rpx;
        color: #fff;
        background: #d2353e;
        border-radius: 11rpx 0rpx 11rpx 0rpx;
      }
    }

    .activity-info {
      flex: 1;
      .activity-title {
        font-weight: 300;
        font-size: 24rpx;
        color: #1a1a1a;
      }
    }
  }
}

// 话题列表样式
.topic-list {
  .topic-item {
    display: flex;
    padding: 20rpx;
    margin-bottom: 20rpx;
    background: #fff;

    .topic-image-wrapper {
      margin-right: 20rpx;

      .topic-image {
        width: 148rpx;
        height: 148rpx;
        border-radius: 20rpx;
      }
    }

    .topic-info {
      flex: 1;

      .topic-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #303030;
        line-height: 1.4;
        margin-bottom: 40rpx;
      }
      .topic-author {
        display: flex;
        align-items: center;
        .author-avatar {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          margin-right: 12rpx;
        }

        .author-name {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
  }
}

// 发帖按钮样式
.post-btn {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 136rpx;
  height: 68rpx;
  background: #d2353e;
  box-shadow: 0rpx 6rpx 17rpx 4rpx rgba(210, 53, 62, 0.35);
  border-radius: 34rpx;
  z-index: 100;
  column-gap: 10rpx;
  .iconfont {
    margin-right: 8rpx;
    font-size: 24rpx;
    color: #fff;
  }

  font-size: 24rpx;
  color: #fff;
}
</style>
