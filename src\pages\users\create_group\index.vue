<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '创建车友群',
    //导航栏颜色
    navigationBarBackgroundColor: '#FFDFE0',
  },
}
</route>

<template>
  <view class="page-container">
    <view class="wrap">
      <!-- 页面标题和描述 -->
      <view class="header-section">
        <view class="page-title">创建车友群</view>
        <view class="page-desc">可上传自己的车友社群，或您加入的车友社群</view>
      </view>

      <!-- 表单区域 -->
      <view class="px-4">
        <view class="form-section">
          <!-- 群名称 -->
          <view class="form-item">
            <view class="form-field">
              <view class="field-label">*群名称</view>
              <view class="field-content">
                <input
                  type="text"
                  v-model="formData.groupName"
                  placeholder="请输入您的车友群名称"
                  class="field-input"
                />
              </view>
            </view>
          </view>

          <!-- 车友群介绍 -->
          <view class="form-item">
            <view class="form-field textarea-field">
              <view class="field-label">*车友群介绍</view>
              <view class="field-content">
                <textarea
                  v-model="formData.groupIntro"
                  placeholder="请简单介绍下您的车友群"
                  class="field-textarea"
                  maxlength="1000"
                />
                <view class="char-count">{{ formData.groupIntro.length }}/1000</view>
              </view>
            </view>
          </view>

          <!-- 活动地址 -->
          <view class="form-item">
            <view class="form-field">
              <view class="field-label">*活动地址</view>
              <view class="field-content">
                <input
                  type="text"
                  v-model="formData.activityAddress"
                  placeholder="请选择省市区地址的格式完整输入"
                  class="field-input"
                />
              </view>
            </view>
          </view>

          <!-- 当前人数 -->
          <view class="form-item">
            <view class="form-field">
              <view class="field-label">*当前人数</view>
              <view class="field-content">
                <input
                  type="number"
                  v-model="formData.currentMembers"
                  placeholder="请输入车友群当前人数"
                  class="field-input"
                />
              </view>
            </view>
          </view>

          <!-- 上传图片 -->
          <view class="form-item">
            <view class="form-field upload-field">
              <view class="field-label">*上传图片</view>
              <view class="upload-section">
                <!-- 车友活动照片 -->
                <view class="upload-group">
                  <view class="upload-title">车友活动照片</view>
                  <view class="upload-item" @click="chooseImage('activity')">
                    <template v-if="uploadedImages.activity">
                      <wd-img
                        :width="100"
                        :height="100"
                        :src="uploadedImages.activity"
                        :radius="8"
                        enable-preview
                      />
                      <view class="delete-btn" @click.stop="deleteImage('activity')">
                        <wd-icon name="close-circle-filled" size="18px"></wd-icon>
                      </view>
                    </template>
                    <template v-else>
                      <view class="upload-placeholder">
                        <wd-icon name="camera" size="24px" color="#CCCCCC"></wd-icon>
                      </view>
                    </template>
                  </view>
                </view>

                <!-- 车友微信群群码 -->
                <view class="upload-group">
                  <view class="upload-title">车友微信群群码</view>
                  <view class="upload-item" @click="chooseImage('wechatCode')">
                    <template v-if="uploadedImages.wechatCode">
                      <wd-img
                        :width="100"
                        :height="100"
                        :src="uploadedImages.wechatCode"
                        :radius="8"
                        enable-preview
                      />
                      <view class="delete-btn" @click.stop="deleteImage('wechatCode')">
                        <wd-icon name="close-circle-filled" size="18px"></wd-icon>
                      </view>
                    </template>
                    <template v-else>
                      <view class="upload-placeholder">
                        <wd-icon name="camera" size="24px" color="#CCCCCC"></wd-icon>
                      </view>
                    </template>
                  </view>
                </view>

                <!-- 车友其他照片 -->
                <view class="upload-group">
                  <view class="upload-title">车友其他照片</view>
                  <view class="upload-item" @click="chooseImage('other')">
                    <template v-if="uploadedImages.other">
                      <wd-img
                        :width="100"
                        :height="100"
                        :src="uploadedImages.other"
                        :radius="8"
                        enable-preview
                      />
                      <view class="delete-btn" @click.stop="deleteImage('other')">
                        <wd-icon name="close-circle-filled" size="18px"></wd-icon>
                      </view>
                    </template>
                    <template v-else>
                      <view class="upload-placeholder">
                        <wd-icon name="camera" size="24px" color="#CCCCCC"></wd-icon>
                      </view>
                    </template>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 加群方式 -->
          <view class="form-item">
            <view class="form-field upload-field">
              <view class="field-label">*加群方式</view>
              <view class="join-method-section">
                <!-- 车友群二维码 -->
                <view class="upload-group">
                  <view class="upload-title">车友群二维码</view>
                  <view class="upload-item" @click="chooseImage('groupQrCode')">
                    <template v-if="uploadedImages.groupQrCode">
                      <wd-img
                        :width="100"
                        :height="100"
                        :src="uploadedImages.groupQrCode"
                        :radius="8"
                        enable-preview
                      />
                      <view class="delete-btn" @click.stop="deleteImage('groupQrCode')">
                        <wd-icon name="close-circle-filled" size="18px"></wd-icon>
                      </view>
                    </template>
                    <template v-else>
                      <view class="upload-placeholder">
                        <wd-icon name="camera" size="24px" color="#CCCCCC"></wd-icon>
                      </view>
                    </template>
                  </view>
                </view>

                <!-- 联系人二维码 -->
                <view class="upload-group">
                  <view class="upload-title">联系人二维码</view>
                  <view class="upload-item" @click="chooseImage('contactQrCode')">
                    <template v-if="uploadedImages.contactQrCode">
                      <wd-img
                        :width="100"
                        :height="100"
                        :src="uploadedImages.contactQrCode"
                        :radius="8"
                        enable-preview
                      />
                      <view class="delete-btn" @click.stop="deleteImage('contactQrCode')">
                        <wd-icon name="close-circle-filled" size="18px"></wd-icon>
                      </view>
                    </template>
                    <template v-else>
                      <view class="upload-placeholder">
                        <wd-icon name="camera" size="24px" color="#CCCCCC"></wd-icon>
                      </view>
                    </template>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="bottom-section">
        <button
          class="submit-btn"
          :class="{ 'button-hover': isFormValid }"
          :disabled="!isFormValid"
          @click="handleSubmit"
        >
          提交资料
        </button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useToast } from 'wot-design-uni'

const toast = useToast()

// 表单数据
const formData = ref({
  groupName: '',
  groupIntro: '',
  activityAddress: '',
  currentMembers: '',
})

// 上传的图片
const uploadedImages = ref({
  activity: '',
  wechatCode: '',
  other: '',
  groupQrCode: '',
  contactQrCode: '',
})

// 当前上传的图片类型
const currentUploadType = ref('')

// 表单验证
const isFormValid = computed(() => {
  return (
    formData.value.groupName.trim() !== '' &&
    formData.value.groupIntro.trim() !== '' &&
    formData.value.activityAddress.trim() !== '' &&
    formData.value.currentMembers.trim() !== ''
  )
})

// 选择图片
const chooseImage = (type: string) => {
  currentUploadType.value = type
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      // 这里应该调用上传接口，暂时直接使用本地路径
      uploadedImages.value[type] = res.tempFilePaths[0]
      toast.success('图片上传成功')
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
      toast.error('选择图片失败')
    },
  })
}

// 删除图片
const deleteImage = (type: string) => {
  uploadedImages.value[type] = ''
  toast.success('图片删除成功')
}

// 提交表单
const handleSubmit = () => {
  if (!isFormValid.value) {
    toast.error('请完善所有必填信息')
    return
  }

  const submitData = {
    ...formData.value,
    images: uploadedImages.value,
  }

  console.log('提交数据:', submitData)
  toast.success('提交成功')

  // 这里可以调用提交接口
  // 提交成功后可以跳转或返回
  setTimeout(() => {
    uni.navigateBack()
  }, 1500)
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: #f5f5f5;
  &::after {
    content: '';
    display: block;
    width: 100%;
    height: 200rpx;
    background: linear-gradient(0deg, #f0f0f0 0%, #ffdddf 99%);
    position: absolute;
    left: 0;
    top: 0;
  }
}
.wrap {
  position: relative;
  z-index: 999;
}

.header-section {
  padding: 40rpx 30rpx 30rpx;
  .page-title {
    margin-bottom: 20rpx;
    font-size: 48rpx;
    font-weight: bold;
    color: #333;
  }

  .page-desc {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
  }
}

.form-section {
  padding: 40rpx 30rpx;
  background-color: white;
  border-radius: 32rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-field {
  .field-label {
    margin-bottom: 28rpx;
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
  }

  .field-content {
    .field-input {
      font-size: 28rpx;
      color: #333;
      border: none;
      outline: none;
      background: #f5f5f5;
      border-radius: 11rpx;
      padding: 20rpx 22rpx;

      &::placeholder {
        color: #999;
      }
    }
  }
}

.textarea-field {
  .field-content {
    position: relative;
    .field-textarea {
      width: 100%;
      box-sizing: border-box;
      min-height: 200rpx;
      font-size: 28rpx;
      color: #333;
      border: none;
      outline: none;
      resize: none;
      background: #f5f5f5;
      border-radius: 11rpx;
      padding: 18rpx 20rpx;

      &::placeholder {
        color: #999;
      }
    }

    .char-count {
      margin-top: 20rpx;
      font-size: 24rpx;
      color: #999;
      text-align: right;
      position: absolute;
      right: 20rpx;
      bottom: 40rpx;
    }
  }
}

.upload-field {
  .upload-section,
  .join-method-section {
    display: flex;
    flex-wrap: wrap;
    gap: 40rpx;
  }

  .upload-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    min-width: 160rpx;

    .upload-title {
      margin-bottom: 20rpx;
      font-size: 24rpx;
      color: #666;
      text-align: center;
    }

    .upload-item {
      position: relative;
      width: 160rpx;
      height: 160rpx;
      background: #f8f8f8;
      border: 2rpx dashed #ddd;
      border-radius: 12rpx;
      overflow: hidden;

      .upload-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
      }

      .delete-btn {
        position: absolute;
        top: -10rpx;
        right: -10rpx;
        z-index: 10;
        color: #f92560;
      }

      &:active {
        opacity: 0.8;
      }
    }
  }
}

.bottom-section {
  padding: 60rpx 30rpx;
  padding-bottom: calc(60rpx + env(safe-area-inset-bottom));

  .submit-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 88rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #fff;
    background: linear-gradient(90deg, #f9225e, #ff94b2);
    border: none;
    border-radius: 44rpx;

    &[disabled] {
      color: #fff;
      background: #f1d6d7;
    }

    &:not([disabled]):active {
      opacity: 0.8;
    }
  }
}

.button-hover {
  opacity: 0.8;
}
</style>
