import { http } from '@/utils/http'
import { pretty } from '@/utils/index'

enum Api {
  authType = '/api/v2/routine/auth_type',
  authLogin = '/api/v2/routine/auth_login',
  authBindingPhone = '/api/v2/routine/auth_binding_phone',
  userinfo = '/api/userinfo',
  getAgreement = '/api/get_agreement',
  banners = '/api/xq/banners',
  getOpenCity = '/api/xq/getOpenCity',
  getMessageList = '/api/xq/user/getMessageList',
  getCardList = '/api/xq/card/list',
  getCardPay = '/api/xq/cardPay',
  hotActivities = '/api/xq/hotActivities',
  hotTopics = '/api/xq/hotTopics',
}

interface IAuthType {
  code: string
}

/**
 * 登录接口-小程序登录缓存key
 */
export const authType = (code: string) => {
  return pretty(http.get<IAuthType>(Api.authType, { code }))
}

interface ILogin {
  code: string
}
/**
 * 登录接口-不强制绑定手机登录，返回token
 */
export const authLogin = (key: string) => {
  return pretty(http.get<ILogin>(Api.authLogin, { key }))
}
/**
 * 登录接口-强制绑定手机登录，返回token
 */
export const authBindingPhone = (params) => {
  return pretty(http.get<ILogin>(Api.authBindingPhone, params))
}

/**
 * 获取登录用户信息
 */
export const userinfo = () => {
  return pretty(http.get(Api.userinfo))
}

/**
 * 获取协议
 */
export const getAgreement = (id) => {
  return pretty(http.get(`${Api.getAgreement}/${id}`))
}

/**
 *获取系统banners
 * @returns
 */
export const getBanners = () => {
  return pretty(http.get(`${Api.banners}`))
}

/**
 *获取开通城市
 * @returns
 */
export const getOpenCity = () => {
  return pretty(http.get(`${Api.getOpenCity}`))
}

/**
 *获取消息列表
 * @returns
 */
export const getMessageList = (params) => {
  return pretty(http.get(`${Api.getMessageList}`, params))
}

/**
 *获取会员卡列表
 * @returns
 */
export const getCardList = (params) => {
  return pretty(http.get(`${Api.getCardList}`, params))
}

/**
 * 获取会员卡支付
 * @returns
 */
export const postCardPay = (id: string) => {
  return pretty(http.post(`${Api.getCardPay}/${id}`))
}

/**
 * 获取热门活动
 * @returns
 */
export const getHotActivities = (params?: { limit?: number }) => {
  return pretty(http.get(Api.hotActivities, params))
}

/**
 * 获取热门话题
 * @returns
 */
export const getHotTopics = (params?: { limit?: number }) => {
  return pretty(http.get(Api.hotTopics, params))
}
