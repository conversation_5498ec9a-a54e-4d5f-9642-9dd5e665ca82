<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '热门板块演示',
  },
}
</route>

<template>
  <view class="demo-wrapper">
    <!-- 热门活动板块 -->
    <view class="hot-section">
      <view class="section-header">
        <view class="section-title">热门活动</view>
        <view class="more-btn">
          更多活动
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
      <view class="activity-list">
        <view
          v-for="item in mockActivities"
          :key="item.id"
          class="activity-item"
        >
          <view class="activity-image-wrapper">
            <image
              class="activity-image"
              :src="item.image"
              mode="aspectFill"
            ></image>
            <view class="activity-status">报名中</view>
          </view>
          <view class="activity-info">
            <view class="activity-title">{{ item.title }}</view>
            <view class="activity-meta">
              <view class="activity-location">
                <text class="iconfont icon-dizhi1"></text>
                <text class="location-text">{{ item.address }}</text>
              </view>
              <view class="activity-time">
                <text class="iconfont icon-shijian"></text>
                <text class="time-text">{{ item.time }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 热门话题板块 -->
    <view class="hot-section">
      <view class="section-header">
        <view class="section-title">热门话题</view>
        <view class="more-btn">
          更多话题
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
      <view class="topic-list">
        <view
          v-for="item in mockTopics"
          :key="item.id"
          class="topic-item"
        >
          <view class="topic-image-wrapper">
            <image
              class="topic-image"
              :src="item.image"
              mode="aspectFill"
            ></image>
          </view>
          <view class="topic-info">
            <view class="topic-title">{{ item.title }}</view>
            <view class="topic-author">
              <image
                class="author-avatar"
                :src="item.user.avatar"
                mode="aspectFill"
              ></image>
              <text class="author-name">{{ item.user.nickname }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 发帖按钮 -->
    <view class="post-btn">
      <text class="iconfont icon-add"></text>
      发帖
    </view>
  </view>
</template>

<script lang="ts" setup>
// 模拟数据
const mockActivities = ref([
  {
    id: 1,
    title: '川西大环线7天6晚',
    address: '稻城亚丁+四姑娘山+色达',
    time: '8月26日 周一',
    image: '/static/images/banner.png'
  },
  {
    id: 2,
    title: '特斯拉后备箱集市',
    address: '成都市天府新区',
    time: '8月28日 周三',
    image: '/static/images/bg1.png'
  }
])

const mockTopics = ref([
  {
    id: 1,
    title: '请问特斯拉4S店贴的3M膜怎么样？',
    image: '/static/images/banner.png',
    user: {
      nickname: '美丽人生',
      avatar: '/static/images/login-female-default.png'
    }
  },
  {
    id: 2,
    title: '贴车衣有必要吗？',
    image: '/static/images/bg1.png',
    user: {
      nickname: '美丽人生',
      avatar: '/static/images/login-male-default.png'
    }
  }
])
</script>

<style lang="scss" scoped>
.demo-wrapper {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

// 热门板块样式
.hot-section {
  margin-bottom: 60rpx;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30rpx;

    .section-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #303030;
    }

    .more-btn {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      color: #999;

      .iconfont {
        margin-left: 8rpx;
        font-size: 20rpx;
      }
    }
  }
}

// 活动列表样式
.activity-list {
  .activity-item {
    display: flex;
    padding: 20rpx;
    margin-bottom: 20rpx;
    background: #fff;
    border-radius: 20rpx;
    box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.1);

    .activity-image-wrapper {
      position: relative;
      margin-right: 20rpx;

      .activity-image {
        width: 160rpx;
        height: 160rpx;
        border-radius: 16rpx;
      }

      .activity-status {
        position: absolute;
        top: 10rpx;
        left: 10rpx;
        padding: 4rpx 12rpx;
        font-size: 20rpx;
        color: #fff;
        background: rgba(249, 34, 94, 0.8);
        border-radius: 12rpx;
      }
    }

    .activity-info {
      flex: 1;

      .activity-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #303030;
        line-height: 1.4;
        margin-bottom: 20rpx;
      }

      .activity-meta {
        .activity-location,
        .activity-time {
          display: flex;
          align-items: center;
          margin-bottom: 12rpx;

          .iconfont {
            margin-right: 8rpx;
            font-size: 24rpx;
            color: #f9225e;
          }

          .location-text,
          .time-text {
            font-size: 24rpx;
            color: #666;
          }
        }
      }
    }
  }
}

// 话题列表样式
.topic-list {
  .topic-item {
    display: flex;
    padding: 20rpx;
    margin-bottom: 20rpx;
    background: #fff;
    border-radius: 20rpx;
    box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.1);

    .topic-image-wrapper {
      margin-right: 20rpx;

      .topic-image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 16rpx;
      }
    }

    .topic-info {
      flex: 1;

      .topic-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #303030;
        line-height: 1.4;
        margin-bottom: 20rpx;
      }

      .topic-author {
        display: flex;
        align-items: center;

        .author-avatar {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          margin-right: 12rpx;
        }

        .author-name {
          font-size: 24rpx;
          color: #666;
        }
      }
    }
  }
}

// 发帖按钮样式
.post-btn {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(-31deg, #ff94b2, #f9225e);
  border-radius: 50%;
  box-shadow: 0rpx 4rpx 13rpx 2rpx rgba(0, 0, 0, 0.3);
  z-index: 100;

  .iconfont {
    margin-right: 8rpx;
    font-size: 24rpx;
    color: #fff;
  }

  font-size: 24rpx;
  color: #fff;
}
</style>
